<?php

namespace App\Livewire\Dashboard\Widgets\Navigation;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Traits\LivewireGeneralFunctions;
use App\Services\Calculation\FrequencyService;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;

class OwnTopic extends Component
{
   use LivewireGeneralFunctions;

    public $userDetails;
    public $topicText = '';
    public $frequency = 0;
    public $timeDisplay = '';
    public $timeSeconds = 0;
    public $showFrequency = false;
    public $showTime = false;
    public $isCalculating = false;
    public $farbklang = false;
    public $showFrequencyGenerator = false;

    // Frequency Generator properties
    public $frequencyHz = '';
    public $frequencyTime = '';
    public $poolId;
    public $widget = [];
    public $biorythDetails;

    protected $rules = [
        'topicText' => 'nullable|string|max:1000',
        'frequencyHz' => 'nullable|numeric|min:250|max:20000',
        'frequencyTime' => 'nullable|numeric|min:5|max:3600',
    ];

    /**
     * Get FrequencyService from container
     *
     * @return FrequencyService
     */
    protected function getFrequencyService(): FrequencyService
    {
        return app(FrequencyService::class);
    }

    public function mount($poolId = null, $widget = [])
    {
        $this->poolId = $poolId;
        $this->widget = $widget;
        $this->userDetails = getUserDetails();
        $this->biorythDetails = biorythVisibleDetails();
        $this->topicText = $this->userDetails->thema_speichern ?? '';

        // Initialize frequency and time if topic exists
        if (!empty($this->topicText)) {
            $this->updateFrequencyFromTopic();
        }

        // Initialize frequency generator values
        $this->initializeFrequencyGenerator();
    }

    public function updatedTopicText()
    {
        if ($this->showFrequencyGenerator) {
            $this->updateFrequencyGeneratorFromTopic();
        } else {
            $this->calculateFrequencyIfNeeded();
        }
    }

    /**
     * Update frequency generator values when topic text changes
     *
     * @return void
     */
    protected function updateFrequencyGeneratorFromTopic(): void
    {
        if (!empty(trim($this->topicText)) && strlen(trim($this->topicText)) > 2) {
            $userId = getUserId();

            $this->getFrequencyService()->updateFrequencyFromTopic(
                $this->topicText,
                $userId,
                function($frequency) {
                    $this->frequencyHz = (string)$frequency;
                    // Also generate new random time when frequency is updated
                    $randomTime = $this->getFrequencyService()->generateRandomTime($this->biorythDetails);
                    $this->frequencyTime = (string)$randomTime;
                },
                function($result) {
                    if ($result['rate_limited']) {
                        $this->showToastr('warning', trans('action.frequency_generator'), $result['message']);
                    }
                }
            );
        } else {
            // Reset frequency generator values for empty or very short text
            $this->frequencyHz = '';
            $this->frequencyTime = '';
        }
    }

    public function calculateFrequencyIfNeeded()
    {
        // Only update frequency if text is not empty and has meaningful content
        if (!empty(trim($this->topicText)) && strlen(trim($this->topicText)) > 2) {
            $this->updateFrequencyFromTopic();
        } else {
            // Reset frequency display for empty or very short text
            $this->frequency = 0;
            $this->timeSeconds = 0;
            $this->timeDisplay = '';
            $this->showFrequency = false;
            $this->showTime = false;
        }
    }

    protected function updateFrequencyFromTopic()
    {
        // Don't recalculate if already calculating
        if ($this->isCalculating) {
            return;
        }

        $this->isCalculating = true;

        try {
            // Calculate frequency using FrequencyService
            $this->frequency = $this->getFrequencyService()->calculateFrequencySafely($this->topicText, [
                'apply_transformations' => true,
                'fallback_frequency' => 250
            ]);

            if ($this->frequency > 0) {
                // Generate random time using FrequencyService
                $this->timeSeconds = $this->getFrequencyService()->generateRandomTime();
                $this->timeDisplay = gmdate('i:s', $this->timeSeconds);
                $this->showFrequency = true;
                $this->showTime = true;
            } else {
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
            }
        } catch (\Exception $e) {
            Log::error('Topic frequency calculation failed', [
                'error' => $e->getMessage(),
                'topic' => substr($this->topicText, 0, 50) . '...'
            ]);
            // Don't show error toastr during typing, just log it
        } finally {
            $this->isCalculating = false;
        }
    }

    protected function isTopicTextEmpty(): bool
    {
        return empty(trim($this->topicText));
    }

    protected function rateLimitKey(): string
    {
        return 'cart_add_' . getUserId(); // assumes getUserId() is globally available
    }

    protected function hasExceededRateLimit(): bool
    {
        return RateLimiter::tooManyAttempts($this->rateLimitKey(), 10);
    }

    protected function ensureFrequencyIsSet(): void
    {
        if ($this->frequency <= 0) {
            $this->frequency = $this->calculateFrequencySafely($this->topicText);
        }
    }

    protected function ensureTimeSecondsIsSet(): void
    {
        if ($this->timeSeconds <= 0) {
            $details = biorythVisibleDetails();
            $this->timeSeconds = $details
                ? rand($details->gs_min_price, $details->gs_max_price)
                : rand(60, 300);
        }
    }

    protected function prepareCartData(): Request
    {
        return new Request([
            'ana_id'      => 1,
            'name'        => trim($this->topicText),
            'submenu_id'  => '',
            'proID'       => '',
            'calculation' => '',
            'male'        => '',
            'heart'       => '',
            'price'       => (int) $this->timeSeconds,
            'causes_id'   => '',
            'medium_id'   => '',
            'tipp_id'     => '',
            'color'       => '',
            'type'        => 'Topic',
            'frequency'   => (int) $this->frequency,
            'time'        => (int) $this->timeSeconds,
        ]);
    }

    protected function warnUser(string $messageKey): void
    {
        $this->showToastr('warning', trans('action.warning'), trans("action.$messageKey"));
    }

    protected function successUser(string $messageKey): void
    {
        $this->showToastr('success', trans('action.success'), trans("action.$messageKey"));
    }

    protected function errorUser(string $messageKey): void
    {
        $this->showToastr('error', trans('action.error'), trans("action.$messageKey"));
    }

    protected function logCartError(\Throwable $e): void
    {
        Log::error('Cart addition failed', [
            'error' => $e->getMessage(),
            'topic' => substr($this->topicText, 0, 50) . '...',
            'trace' => $e->getTraceAsString(),
        ]);
    }

    public function addToCart(): void
    {
        if ($this->isTopicTextEmpty()) {
            $this->warnUser('no_product_title');
            return;
        }

        if ($this->hasExceededRateLimit()) {
            $this->warnUser('cart_max_allow_alert');
            return;
        }

        RateLimiter::hit($this->rateLimitKey(), 60);

        $this->ensureFrequencyIsSet();
        $this->ensureTimeSecondsIsSet();

        try {
            $response = $this->getFrequencyService()->submitToSajaxController($this->prepareCartData());

            $data = $response->getData(true);

            if ($data['success'] ?? false) {
                $this->successUser('added_successfully');
                $this->dispatch('cartUpdated');
            } else {
                $this->errorUser($data['message'] ?? 'cart_add_failed');
            }
        } catch (\Throwable $e) {
            $this->logCartError($e);
            $this->errorUser('cart_add_failed');
        }
    }
 

    public function saveTopic()
    {
        try {
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => $this->topicText]);

            if ($updated) {
                $this->showToastr('success', trans('action.success'), trans('action.topic_saved_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic save failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_save_failed'));
        }
    }

    public function deleteTopic()
    {
        try {
            $this->topicText = '';
            $updated = DB::table('users')
                ->where('id', getUserId())
                ->update(['thema_speichern' => '']);

            if ($updated) {
                $this->frequency = 0;
                $this->timeSeconds = 0;
                $this->timeDisplay = '';
                $this->showFrequency = false;
                $this->showTime = false;
                $this->showToastr('success', trans('action.success'), trans('action.topic_deleted_successfully'));
                // Update userDetails to reflect the change
                $this->userDetails = getUserDetails();
            } else {
                $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
            }
        } catch (\Exception $e) {
            Log::error('Topic delete failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.error'), trans('action.topic_delete_failed'));
        }
    }

    /**
     * Initialize frequency generator values
     *
     * @return void
     */
    protected function initializeFrequencyGenerator(): void
    {
        // Generate random time within bounds using FrequencyService
        $randomTime = $this->getFrequencyService()->generateRandomTime($this->biorythDetails);

        // Get topic and calculate frequency
        $topicText = $this->getFrequencyService()->sanitizeInput($this->userDetails->thema_speichern ?? '');

        $calculatedFrequency = 0;
        if (!empty($topicText)) {
            $calculatedFrequency = $this->getFrequencyService()->calculateFrequencySafely($topicText);
        }

        // Set initial values
        $this->frequencyHz = $calculatedFrequency > 0 ? (string)$calculatedFrequency : '';
        $this->frequencyTime = (string)$randomTime;
    }

    /**
     * Calculate harmonics for frequency generator
     *
     * @return void
     */
    public function calculateHarmonics(): void
    {
        if (empty($this->frequencyHz) || !is_numeric($this->frequencyHz)) {
            $this->showToastr('warning', trans('action.frequency_generator'), trans('action.enter_valid_frequency'));
            return;
        }

        try {
            $frequency = (float) $this->frequencyHz;

            // Validate frequency range using FrequencyService
            if (!$this->getFrequencyService()->validateFrequency((int)$frequency)) {
                $this->showToastr('error', trans('action.frequency_generator'), trans('action.invalid_frequency'));
                return;
            }

            // Calculate harmonics using FrequencyService
            $harmonics = $this->getFrequencyService()->generateHarmonics($frequency);

            // Generate HTML table for modal using FrequencyService
            $harmonicsHtml = $this->getFrequencyService()->renderHarmonicsTable($harmonics);

            // Show in modal
            $this->showModal($harmonicsHtml, trans('action.calculate_harmonics'));

            Log::info('Harmonics calculated', [
                'frequency' => $frequency,
                'user_id' => getUserId()
            ]);

        } catch (\Exception $e) {
            Log::error('OwnTopic calculateHarmonics error', [
                'error' => $e->getMessage(),
                'frequency' => $this->frequencyHz
            ]);

            $this->showToastr('error', trans('action.frequency_generator'), trans('action.error_occurred'));
        }
    }

    /**
     * Add frequency generator item to cart
     *
     * @return void
     */
    public function addFrequencyGeneratorToCart(): void
    {
        if (empty($this->topicText)) {
            $this->showToastr('warning', trans('action.frequency_generator'), trans('action.topic_name_required'));
            return;
        }

        if (empty($this->frequencyHz) || !is_numeric($this->frequencyHz)) {
            $this->showToastr('warning', trans('action.frequency_generator'), trans('action.enter_valid_frequency'));
            return;
        }

        if (empty($this->frequencyTime) || !is_numeric($this->frequencyTime)) {
            $this->showToastr('warning', trans('action.frequency_generator'), trans('action.enter_valid_time'));
            return;
        }

        try {
            $cartData = new Request([
                'ana_id'      => 1,
                'name'        => trim($this->topicText),
                'submenu_id'  => '',
                'proID'       => '',
                'calculation' => '',
                'male'        => '',
                'heart'       => '',
                'price'       => (int) $this->frequencyTime,
                'causes_id'   => '',
                'medium_id'   => '',
                'tipp_id'     => '',
                'color'       => '',
                'type'        => 'FrequencyGenerator',
                'frequency'   => (int) $this->frequencyHz,
                'time'        => (int) $this->frequencyTime,
            ]);

            $response = $this->getFrequencyService()->submitToSajaxController($cartData);
            $data = $response->getData(true);

            if ($data['success'] ?? false) {
                $this->showToastr('success', trans('action.frequency_generator'), trans('action.added_successfully'));
                $this->dispatch('cartUpdated');
            } else {
                $this->showToastr('error', trans('action.frequency_generator'), $data['message'] ?? trans('action.cart_add_failed'));
            }
        } catch (\Throwable $e) {
            Log::error('Frequency generator cart add failed', [
                'error' => $e->getMessage(),
                'user_id' => getUserId()
            ]);
            $this->showToastr('error', trans('action.frequency_generator'), trans('action.cart_add_failed'));
        }
    }

    /**
     * Toggle between Own Topic and Frequency Generator views
     *
     * @return void
     */
    public function toggleFrequencyGenerator(): void
    {
        $this->showFrequencyGenerator = !$this->showFrequencyGenerator;
    }

    public function render()
    {
        return view('livewire.dashboard.widgets.navigation.own-topic');
    }
}
