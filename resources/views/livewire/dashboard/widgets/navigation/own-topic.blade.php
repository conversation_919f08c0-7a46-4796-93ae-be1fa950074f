<div>
    <style>
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .grid-stack-item-content,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .widget-body,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-container,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-body {
            overflow: visible !important;
        }

        /* Frequency Generator Animations */
        @keyframes glowInput {
            0%, 100% { box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); }
            50% { box-shadow: 0 0 20px rgba(0, 123, 255, 0.8); }
        }

        @keyframes glowText {
            0%, 100% { color: #007bff; }
            50% { color: #0056b3; }
        }

        @keyframes glowButton {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
            50% { box-shadow: 0 0 15px rgba(255, 193, 7, 0.8); }
        }

        @keyframes glowPrimary {
            0%, 100% { box-shadow: 0 0 5px rgba(0, 123, 255, 0.5); }
            50% { box-shadow: 0 0 15px rgba(0, 123, 255, 0.8); }
        }

        .glowing-input {
            animation: glowInput 1.5s ease-in-out infinite;
        }

        .glowing-text {
            animation: glowText 1.5s ease-in-out infinite;
        }

        .glowing-button {
            animation: glowButton 1.5s ease-in-out infinite;
        }

        .btn-primary.glowing-button {
            animation: glowPrimary 1.5s ease-in-out infinite;
        }

        .position-relative {
            position: relative;
        }

        .position-absolute {
            z-index: 10;
        }

        .frequency-time-controls {
            margin-bottom: 1rem;
        }

        .frequency-input-group, .time-input-group {
            margin-bottom: 0.5rem;
        }

        .harmonics-btn {
            border-radius: 0 0.375rem 0.375rem 0;
        }
    </style>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">

    <div class="topicTab fade show own-topic-text-div" id="nav-widget-topicTab-{{ $widget['id'] }}"
        style="background: #fff;background-clip: padding-box;">
        <div class="arrow"></div>
        <form wire:submit.prevent="">
            <div class="row">
                <div class="col-6 d-flex align-items-center">
                    @if(!$showFrequencyGenerator)
                        @if($showFrequency)
                        <small class="form-text text-muted">{{ $frequency }}</small>
                        <small class="form-text text-muted" style="margin-left: 2px;">{{trans('action.unit_hertz')}}</small>
                        @endif
                    @endif
                </div>
                <div class="col-6 d-flex align-items-center justify-content-end">
                    <!-- Switch Button for Own Topic only -->
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary me-2"
                            wire:click="toggleFrequencyGenerator"
                            title="{{ $showFrequencyGenerator ? trans('action.show_own_topic') : trans('action.show_frequency_generator') }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="top">
                        <i class="fas {{ $showFrequencyGenerator ? 'fa-edit' : 'fa-wave-square' }}"></i>
                    </button>

                    @if(!$showFrequencyGenerator)
                        @if($showTime)
                        <small class="form-text text-muted text-right" style="color: #28a745;">{{ $timeDisplay }}</small>
                        <small class="form-text text-muted" style="margin-left: 2px;">{{trans('action.unit_seconds')}}</small>
                        @endif
                    @endif
                </div>
            </div>

            @if(!$showFrequencyGenerator)
                <!-- Own Topic View -->
                <div class="form-group topic">
                    <textarea wire:model.blur="topicText"
                        wire:keyup.debounce.1000ms="calculateFrequencyIfNeeded"
                        rows="2"
                        class="form-control"
                        placeholder="{{trans('action.topic_name_placeholder')}}"></textarea>
                    @if($isCalculating)
                    <small class="form-text text-muted">{{trans('action.calculating_frequency')}}...</small>
                    @endif
                </div>
                <div class="text-center topic-btns">
                    @if(!$farbklang)
                    <button type="button" class="btn btn-primary icon"
                        wire:click="addToCart"
                        @if($isCalculating || empty($topicText)) disabled @endif>
                        {{trans('action.cart')}}
                    </button>
                    @endif
                    <button type="button" class="btn btn-success icon"
                        wire:click="saveTopic"
                        @if($isCalculating) disabled @endif>
                        {{trans('action.save')}}
                    </button>
                    <button type="button" class="btn btn-danger icon"
                        wire:click="deleteTopic"
                        @if($isCalculating) disabled @endif>
                        {{trans('action.delete')}}
                    </button>
                </div>
            @else
                <!-- Frequency Generator View -->
                <div class="form-group topic">
                    <div class="position-relative">
                        <textarea rows="2" class="form-control" wire:model.live.debounce.500ms="topicText"
                            wire:loading.class="glowing-input" wire:target="topicText"
                            placeholder="{{ trans('action.topic_name_placeholder') }}"></textarea>
                        <div wire:loading wire:target="topicText" class="position-absolute top-0 end-0 p-2">
                            <small class="text-muted glowing-text">
                                <i class="fas fa-calculator"></i>
                            </small>
                        </div>
                    </div>
                    @error('topicText')
                    <small class="text-danger">{{ $message }}</small>
                    @enderror
                </div>
                <div class="frequency-time-controls">
                    <div class="frequency-input-group">
                        <div class="input-group">
                            <input type="number" class="form-control" style="border: 0 !important;" min="250"
                                max="20000" step="50" placeholder="{{ trans('action.frequency_placeholder') }}"
                                wire:model.defer="frequencyHz" wire:loading.class="glowing-input"
                                wire:target="topicText" wire:loading.attr="readonly" wire:target="topicText">
                            <span class="input-group-text" wire:loading.class="glowing-text"
                                wire:target="topicText">
                                {{ trans('action.unit_hertz') }}
                            </span>
                            <button type="button" class="btn btn-warning harmonics-btn"
                                wire:click="calculateHarmonics" wire:loading.attr="disabled"
                                wire:loading.class="glowing-button" wire:target="calculateHarmonics"
                                title="{{ __('action.calculate_harmonics') }}" data-bs-toggle="tooltip"
                                data-bs-placement="top">
                                <i class="fas fa-wave-square"></i>
                            </button>
                        </div>
                        @error('frequencyHz')
                        <small class="form-text text-danger">{{ $message }}</small>
                        @enderror
                    </div>
                    <div class="time-input-group">
                        <div class="input-group">
                            <input type="number" class="form-control" style="border: 0 !important;" min="5"
                                max="3600" step="1" placeholder="{{ trans('action.seconds_placeholder') }}"
                                wire:model.defer="frequencyTime" wire:loading.class="glowing-input"
                                wire:target="topicText" wire:loading.attr="readonly" wire:target="topicText">
                            <span class="input-group-text" wire:loading.class="glowing-text"
                                wire:target="topicText">{{ trans('action.unit_seconds') }}</span>
                        </div>
                        @error('frequencyTime')
                        <small class="form-text text-danger">{{ $message }}</small>
                        @enderror
                    </div>
                </div>
                <div class="text-center frequency-btns">
                    <button type="button" class="btn btn-primary icon" wire:click="addFrequencyGeneratorToCart"
                        wire:loading.attr="disabled" wire:loading.class="glowing-button" wire:target="addFrequencyGeneratorToCart">
                        <span>{{ trans('action.cart') }}</span>
                        <span wire:loading wire:target="addFrequencyGeneratorToCart" class="glowing-text">{{
                            trans('action.processing') }}...</span>
                    </button>
                </div>
            @endif
        </form>
    </div>
</div>
