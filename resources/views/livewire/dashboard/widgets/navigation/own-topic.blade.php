<div>
    <style>
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .grid-stack-item-content,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .widget-body,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-container,
        .grid-stack-item[gs-id="{{ $widget['id'] ?? 'default' }}"] .custom-widget-body {
            overflow: visible !important;
        }


    </style>
    <link href="{{ asset('css/dashboard/right-panel.css') }}" rel="stylesheet">

    <div class="topicTab fade show own-topic-text-div" id="nav-widget-topicTab-{{ $widget['id'] }}"
        style="background: #fff;background-clip: padding-box;">
        <div class="arrow"></div>
        <form wire:submit.prevent="">
            <div class="row">
                <div class="col-6 d-flex align-items-center">
                    @if(!$showFrequencyGenerator)
                        @if($showFrequency)
                        <small class="form-text text-muted">{{ $frequency }}</small>
                        <small class="form-text text-muted" style="margin-left: 2px;">{{trans('action.unit_hertz')}}</small>
                        @endif
                    @endif
                </div>
                <div class="col-6 d-flex align-items-center justify-content-end">
                    <!-- Switch Button for Own Topic only -->
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary me-2"
                            wire:click="toggleFrequencyGenerator"
                            title="{{ $showFrequencyGenerator ? trans('action.show_own_topic') : trans('action.show_frequency_generator') }}"
                            data-bs-toggle="tooltip"
                            data-bs-placement="top">
                        <i class="fas {{ $showFrequencyGenerator ? 'fa-edit' : 'fa-wave-square' }}"></i>
                    </button>

                    @if(!$showFrequencyGenerator)
                        @if($showTime)
                        <small class="form-text text-muted text-right" style="color: #28a745;">{{ $timeDisplay }}</small>
                        <small class="form-text text-muted" style="margin-left: 2px;">{{trans('action.unit_seconds')}}</small>
                        @endif
                    @endif
                </div>
            </div>

            @if(!$showFrequencyGenerator)
                <!-- Own Topic View -->
                <div class="form-group topic">
                    <textarea wire:model.blur="topicText"
                        wire:keyup.debounce.1000ms="calculateFrequencyIfNeeded"
                        rows="2"
                        class="form-control"
                        placeholder="{{trans('action.topic_name_placeholder')}}"></textarea>
                    @if($isCalculating)
                    <small class="form-text text-muted">{{trans('action.calculating_frequency')}}...</small>
                    @endif
                </div>
                <div class="text-center topic-btns">
                    @if(!$farbklang)
                    <button type="button" class="btn btn-primary icon"
                        wire:click="addToCart"
                        @if($isCalculating || empty($topicText)) disabled @endif>
                        {{trans('action.cart')}}
                    </button>
                    @endif
                    <button type="button" class="btn btn-success icon"
                        wire:click="saveTopic"
                        @if($isCalculating) disabled @endif>
                        {{trans('action.save')}}
                    </button>
                    <button type="button" class="btn btn-danger icon"
                        wire:click="deleteTopic"
                        @if($isCalculating) disabled @endif>
                        {{trans('action.delete')}}
                    </button>
                </div>
            @else
                <!-- Frequency Generator Component -->
                <livewire:dashboard.widgets.navigation.frequency-generator
                    :poolId="$poolId"
                    :widget="$widget"
                    :wire:key="'embedded-frequency-generator-' . $widget['id'] . '-' . rand()"
                />
            @endif
        </form>
    </div>
</div>
